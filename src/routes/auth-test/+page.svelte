<script lang="ts">
  import { authService } from "$lib/services/auth.service";
  import { userAuth, activationCode } from "$lib/stores/app.store";
  import Button from "$lib/components/ui/Button.svelte";
  import Card from "$lib/components/ui/Card.svelte";
  import { addToast } from "$lib/components/toast/toastStore";

  let isLoading = $state(false);

  // 测试JWT刷新
  async function testTokenRefresh() {
    isLoading = true;
    try {
      const success = await authService.refreshToken();
      addToast({
        type: success ? "success" : "error",
        message: success ? "Token刷新成功" : "Token刷新失败",
      });
    } catch (error) {
      addToast({
        type: "error",
        message: "Token刷新出错",
      });
    } finally {
      isLoading = false;
    }
  }

  // 测试激活码验证
  async function testActivationCode() {
    const code = prompt("请输入测试激活码:");
    if (!code) return;

    isLoading = true;
    try {
      const success = await authService.verifyActivationCode(code);
      addToast({
        type: success ? "success" : "error",
        message: success ? "激活码验证成功" : "激活码验证失败",
      });
    } catch (error) {
      addToast({
        type: "error",
        message: "激活码验证出错",
      });
    } finally {
      isLoading = false;
    }
  }

  // 测试发送验证邮件
  async function testSendVerification() {
    isLoading = true;
    try {
      const success = await authService.sendEmailVerification();
      addToast({
        type: success ? "success" : "error",
        message: success ? "验证邮件发送成功" : "验证邮件发送失败",
      });
    } catch (error) {
      addToast({
        type: "error",
        message: "发送验证邮件出错",
      });
    } finally {
      isLoading = false;
    }
  }

  // 测试密码重置
  async function testPasswordReset() {
    const email = prompt("请输入邮箱地址:");
    if (!email) return;

    isLoading = true;
    try {
      const success = await authService.sendPasswordResetEmail(email);
      addToast({
        type: success ? "success" : "error",
        message: success ? "重置邮件发送成功" : "重置邮件发送失败",
      });
    } catch (error) {
      addToast({
        type: "error",
        message: "发送重置邮件出错",
      });
    } finally {
      isLoading = false;
    }
  }

  // 登出
  async function handleSignOut() {
    await authService.signOut();
  }
</script>

<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-8">认证系统测试页面</h1>

  <!-- 用户状态显示 -->
  <Card class="mb-8">
    <h2 class="text-xl font-semibold mb-4">当前用户状态</h2>
    <div class="space-y-2">
      <p><strong>已登录:</strong> {authService.isAuthenticated() ? "是" : "否"}</p>
      <p><strong>邮箱已验证:</strong> {authService.isEmailVerified() ? "是" : "否"}</p>
      <p><strong>已激活:</strong> {authService.isActivated() ? "是" : "否"}</p>
      
      {#if $userAuth && $userAuth.email}
        <p><strong>邮箱:</strong> {$userAuth.email}</p>
        <p><strong>UID:</strong> {$userAuth.uid}</p>
      {/if}
      
      {#if $activationCode}
        <p><strong>激活码:</strong> {$activationCode}</p>
      {/if}
    </div>
  </Card>

  <!-- 测试功能 -->
  <Card class="mb-8">
    <h2 class="text-xl font-semibold mb-4">测试功能</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Button
        onclick={testTokenRefresh}
        loading={isLoading}
        disabled={!authService.isAuthenticated()}
      >
        测试Token刷新
      </Button>

      <Button
        onclick={testActivationCode}
        loading={isLoading}
        disabled={!authService.isAuthenticated()}
      >
        测试激活码验证
      </Button>

      <Button
        onclick={testSendVerification}
        loading={isLoading}
        disabled={!authService.isAuthenticated() || authService.isEmailVerified()}
      >
        发送验证邮件
      </Button>

      <Button
        onclick={testPasswordReset}
        loading={isLoading}
      >
        测试密码重置
      </Button>

      <Button
        onclick={handleSignOut}
        variant="danger"
        disabled={!authService.isAuthenticated()}
      >
        登出
      </Button>

      <Button
        href="/login"
        variant="outline"
      >
        前往登录页
      </Button>
    </div>
  </Card>

  <!-- 原始数据显示 -->
  <Card>
    <h2 class="text-xl font-semibold mb-4">原始数据</h2>
    <div class="space-y-4">
      <div>
        <h3 class="font-medium mb-2">用户认证数据:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{JSON.stringify($userAuth, null, 2)}</pre>
      </div>
      
      <div>
        <h3 class="font-medium mb-2">激活码:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm">{$activationCode || "未设置"}</pre>
      </div>
    </div>
  </Card>

  <!-- 使用说明 -->
  <Card class="mt-8">
    <h2 class="text-xl font-semibold mb-4">使用说明</h2>
    <div class="prose prose-sm">
      <ol>
        <li>首先前往登录页面进行注册或登录</li>
        <li>登录成功后返回此页面测试各项功能</li>
        <li>测试Token刷新功能确保JWT自动更新正常</li>
        <li>测试激活码验证功能</li>
        <li>测试邮件发送功能（验证邮件、密码重置邮件）</li>
        <li>查看原始数据确认状态管理正确</li>
      </ol>
      
      <h3>注意事项:</h3>
      <ul>
        <li>某些功能需要用户已登录才能使用</li>
        <li>邮件发送功能需要有效的Firebase配置</li>
        <li>激活码验证需要有效的激活码</li>
        <li>所有操作都会显示Toast通知</li>
      </ul>
    </div>
  </Card>
</div>

<style>
  .prose ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
  }
  
  .prose ul {
    list-style-type: disc;
    margin-left: 1.5rem;
  }
  
  .prose li {
    margin: 0.5rem 0;
  }
  
  .prose h3 {
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }
</style>
