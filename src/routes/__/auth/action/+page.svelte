<script lang="ts">
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import Loading from "$lib/components/ui/Loading.svelte";
  import { onMount } from "svelte";

  const mode = page.url.searchParams.get("mode");
  const oobCode = page.url.searchParams.get("oobCode");
  const apiKey = page.url.searchParams.get("apiKey");
  const continueUrl = page.url.searchParams.get("continueUrl");
  const lang = page.url.searchParams.get("lang");

  onMount(() => {
    console.log(new Date(), page.url.toString());
    console.log(
      "mode",
      mode,
      "oobCode",
      oobCode,
      "apiKey",
      apiKey,
      "continueUrl",
      continueUrl,
      "lang",
      lang,
    );
    const params = page.url.searchParams;
    params.delete("continueUrl");
    const target = `${continueUrl}?${params.toString()}`;
    window.location.href = target;
    goto(target);
  });
</script>

<Loading text="登录中..." size="sm"></Loading>
